<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="AI Architecture for Real World Impact - From Problem to Prototype in Days, Not Months" />
    <meta property="og:title" content="Nesy Systems - AI Architecture for Real World Impact" />
    <meta property="og:description" content="Revolutionary AI-augmented systems architecture delivering tangible solutions for complex real-world problems." />
    <title>Nesy Systems - AI Architecture for Real World Impact</title>
  </head>
  <body>
    <div id="app">
      <!-- Landing Experience -->
      <div id="landing-screen" class="landing-screen">
        <div class="landing-content">
          <h1 id="company-name" class="company-name">NESY SYSTEMS</h1>
          <p id="tagline" class="tagline">AI Architecture for Real World Impact</p>
          <button id="explore-btn" class="explore-btn">Explore</button>
        </div>
      </div>

      <!-- Graph Container -->
      <div id="graph-container" class="graph-container hidden">
        <svg id="neural-graph" class="neural-graph"></svg>
      </div>

      <!-- Header (appears after graph) -->
      <header id="main-header" class="main-header hidden">
        <div class="header-content">
          <div class="logo">NESY SYSTEMS</div>
          <button class="cta-button pulse">Book Blueprint Session</button>
        </div>
      </header>

      <!-- Main Content Sections -->
      <main id="main-content" class="main-content hidden">
        <!-- Content will be dynamically loaded based on graph navigation -->
      </main>

      <!-- AI Agent Orb -->
      <div id="ai-agent" class="ai-agent">
        <div class="orb-container">
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
          <div class="particle"></div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
