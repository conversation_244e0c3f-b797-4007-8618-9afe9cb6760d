# Nesy Systems - Master Project Configuration
# AI Architecture for Real World Impact

project:
  name: "Nesy Systems"
  tagline: "AI Architecture for Real World Impact"
  type: "Web Platform"
  version: "1.0.0"
  
company:
  legal_name: "Nesy Systems Architecture AI Design Consultancy"
  short_name: "Nesy Systems"
  location: "Estonia"
  leadership: "CEO/CTO"
  
brand:
  colors:
    primary: "#000000"
    secondary: "#FFFFFF"
    accent: "dynamic" # Only in project thumbnails
  typography:
    headings: "Bold, newspaper-inspired"
    body: "Clean, readable"
  voice:
    - "Straightforward"
    - "Technical but accessible"
    - "Human-centric"
    - "No sales fluff"
    
technical_stack:
  frontend:
    visualization: "D3.js"
    framework: "Vanilla JS or lightweight"
    animations: "CSS3 + JS"
    build_tool: "Vite"
  integrations:
    payment: "Stripe"
    scheduling: "Calendly"
    ai_chatbot: "Custom implementation"
  development:
    ide: "VS Code"
    ai_agents:
      - name: "Augment"
        base: "Claude Sonnet"
        use: "Primary development"
      - name: "Claude Code"
        type: "CLI"
        use: "Architecture decisions"
      - name: "Gemini Pro 2.5"
        type: "CLI"
        use: "Parallel tasks"
        
site_structure:
  landing:
    sequence:
      - action: "Fade in logo"
        duration: "1s"
      - action: "Show tagline"
        duration: "0.5s"
      - action: "Display explore button"
        duration: "0.5s"
    graph:
      type: "D3.js force-directed"
      style: "Organic neural network"
      interaction: "Click to explore nodes"
      
  navigation:
    primary_nodes:
      - id: "blueprint"
        label: "Blueprint Sessions"
        priority: "high"
        purpose: "Revenue generation"
      - id: "portfolio"
        label: "Portfolio"
        showcase:
          - "Computer Vision Algorithm - Spatial"
          - "Computer Vision Algorithm - Safety"
          - "Graph Framework #1"
          - "Graph Framework #2"
          - "Medical Agent"
          - "Conflict Resolution System"
      - id: "approach"
        label: "Our Approach"
      - id: "industries"
        label: "Industries"
        verticals:
          - "Workplace Safety"
          - "Legal/Law Firms"
          - "Medical/Healthcare"
          - "Restaurant/Food"
          - "Hospitality/Tourism"
          - "NGO/Humanitarian"
          - "SME/Startups"
          - "Crypto/Blockchain"
      - id: "contact"
        label: "Contact"
        
  persistent_elements:
    header:
      left: "Logo"
      right: "Book Blueprint Session"
      behavior: "Sticky, subtle pulse on CTA"
    ai_agent:
      position: "bottom-right"
      size: "60px"
      design: "Geometric particles"
      states:
        idle: "Slow float"
        active: "Particle consolidation"
        thinking: "Rapid morph"
        
blueprint_product:
  name: "AI Architecture Blueprint"
  tagline: "From Problem to Prototype in Days, Not Months"
  pricing:
    base: 3000
    max: 5000
    currency: "USD"
    factors: "Complexity scaling"
  deliverables:
    - "Technical feasibility analysis"
    - "Architectural blueprints"
    - "Working prototypes/MVPs"
    - "Complete documentation"
    - "Implementation roadmap"
  sales_assets:
    - "CEO explanation video"
    - "Process infographic"
    - "Case studies"
    - "FAQ section"
    
conversion_paths:
  primary:
    - "Land on site"
    - "Explore graph"
    - "Click Blueprint Sessions"
    - "Watch video"
    - "Book session"
    - "Pay deposit"
  secondary:
    - path: "AI agent conversation"
      goal: "Qualified lead"
    - path: "Portfolio exploration"
      goal: "Project interest"
      
development_phases:
  phase_1:
    name: "Revenue Foundation"
    priority: "Critical"
    tasks:
      - "Basic site structure"
      - "Landing page"
      - "Blueprint Sessions page"
      - "Payment integration"
      - "Contact/booking"
      - "Mobile responsive"
  phase_2:
    name: "Enhanced Experience"
    tasks:
      - "Full D3.js graph"
      - "All section pages"
      - "Portfolio cases"
      - "Basic AI agent"
      - "Animations"
  phase_3:
    name: "Complete Vision"
    tasks:
      - "Full portfolio"
      - "Advanced AI features"
      - "Multi-language"
      - "Analytics"
      - "Blog section"
      
performance_targets:
  lighthouse_score: 90
  first_contentful_paint: "1.5s"
  time_to_interactive: "3s"
  mobile_first: true
  
success_metrics:
  week_1:
    sessions_booked: 1
    unique_visitors: 100
    conversion_rate: "5%"
  month_1:
    sessions_booked: 10
    revenue: 30000
    unique_visitors: 1000
    ai_engagement: "30%"
    
project_config:
  repository: "nesy-systems-web"
  staging_url: "staging.nesysystems.com"
  production_url: "nesysystems.com"
  ssl: true
  cdn: true
  analytics: true