/* Nesy Systems - Master Stylesheet */
/* Monochrome Design with Newspaper-Inspired Typography */

:root {
  /* Brand Colors - Pure Monochrome */
  --primary-black: #000000;
  --primary-white: #FFFFFF;
  --gray-light: #F5F5F5;
  --gray-medium: #CCCCCC;
  --gray-dark: #333333;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Spacing */
  --space-xs: 0.5rem;
  --space-sm: 1rem;
  --space-md: 2rem;
  --space-lg: 4rem;
  --space-xl: 8rem;

  /* Animations */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background-color: var(--primary-black);
  color: var(--primary-white);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Landing Screen Styles */
.landing-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--primary-black);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: var(--transition-smooth);
}

.landing-content {
  text-align: center;
  max-width: 800px;
  padding: var(--space-md);
}

.company-name {
  font-family: var(--font-display);
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  letter-spacing: -0.02em;
  color: var(--primary-white);
  margin-bottom: var(--space-sm);
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease-out 0.5s forwards;
}

.tagline {
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-weight: 300;
  color: var(--gray-medium);
  margin-bottom: var(--space-lg);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 1.5s forwards;
}

.explore-btn {
  background: transparent;
  border: 2px solid var(--primary-white);
  color: var(--primary-white);
  padding: var(--space-sm) var(--space-lg);
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: var(--transition-smooth);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 2.3s forwards;
  text-transform: uppercase;
}

.explore-btn:hover {
  background-color: var(--primary-white);
  color: var(--primary-black);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

/* Graph Container */
.graph-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--primary-white);
  z-index: 900;
  transition: var(--transition-smooth);
}

.neural-graph {
  width: 100%;
  height: 100%;
}

/* Header Styles */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-light);
  z-index: 800;
  transition: var(--transition-smooth);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  max-width: 1400px;
  margin: 0 auto;
}

.logo {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 900;
  color: var(--primary-black);
  letter-spacing: -0.02em;
}

.cta-button {
  background-color: var(--primary-black);
  color: var(--primary-white);
  border: none;
  padding: var(--space-sm) var(--space-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  border-radius: 4px;
}

.cta-button:hover {
  background-color: var(--gray-dark);
  transform: translateY(-1px);
}

.pulse {
  animation: pulse 2s infinite;
}

/* AI Agent Orb */
.ai-agent {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  z-index: 700;
  cursor: pointer;
}

.orb-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: var(--primary-black);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 30%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 70%; animation-delay: 0.6s; }
.particle:nth-child(3) { top: 40%; left: 20%; animation-delay: 1.2s; }
.particle:nth-child(4) { top: 70%; left: 50%; animation-delay: 1.8s; }
.particle:nth-child(5) { top: 30%; left: 80%; animation-delay: 2.4s; }

/* Utility Classes */
.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.main-content {
  position: relative;
  z-index: 600;
  background-color: var(--primary-white);
  color: var(--primary-black);
  min-height: 100vh;
  padding-top: 80px;
}

/* Animations */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-10px) translateX(5px);
  }
  66% {
    transform: translateY(5px) translateX(-5px);
  }
}

/* Graph Enhancements */
.organic-link {
  transition: opacity 0.3s ease;
}

.main-link {
  transition: all 0.3s ease;
}

.main-node:hover + .organic-link {
  opacity: 0.4 !important;
}

/* Subtle breathing animation for organic nodes */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: var(--base-opacity);
  }
  50% {
    transform: scale(1.1);
    opacity: calc(var(--base-opacity) * 1.3);
  }
}

/* Approach Content Panel */
#approach-content-panel {
  position: fixed;
  top: 0;
  right: -500px;
  width: 480px;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-left: 1px solid var(--gray-light);
  z-index: 1100;
  transition: right 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
}

#approach-content-panel.visible {
  right: 0;
}

.content-panel-header {
  padding: var(--space-md);
  border-bottom: 1px solid var(--gray-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.content-panel-header h2 {
  font-size: 2rem;
  font-weight: 900;
  color: var(--primary-black);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  letter-spacing: -0.02em;
}

.back-button {
  background: var(--primary-black);
  color: var(--primary-white);
  border: none;
  padding: var(--space-xs) var(--space-sm);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  border-radius: 4px;
  transition: var(--transition-smooth);
}

.back-button:hover {
  background: var(--gray-dark);
  transform: translateX(-2px);
}

.content-panel-body {
  padding: var(--space-md);
}

.approach-intro {
  margin-bottom: var(--space-lg);
}

.approach-intro h3 {
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary-black);
  margin-bottom: var(--space-sm);
  text-shadow: 0 1px 3px rgba(0,0,0,0.1);
  letter-spacing: -0.01em;
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.approach-intro p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: var(--gray-dark);
  text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.approach-sections {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.approach-section {
  padding: var(--space-md);
  background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 4px solid transparent;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}

.approach-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0,0,0,0.02) 0%, rgba(0,0,0,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.approach-section:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);
  border-left-color: var(--primary-black);
  transform: translateX(6px) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.approach-section:hover::before {
  opacity: 1;
}

.approach-section.highlighted {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: white;
  border-left-color: #ffffff;
  transform: translateX(8px) translateY(-3px);
  box-shadow: 0 12px 30px rgba(0,0,0,0.25);
}

.approach-section.highlighted h4,
.approach-section.highlighted p {
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.approach-section h4 {
  font-size: 1.3rem;
  font-weight: 800;
  color: var(--primary-black);
  margin-bottom: var(--space-xs);
  text-shadow: 0 1px 2px rgba(0,0,0,0.05);
  letter-spacing: -0.01em;
}

.approach-section p {
  font-size: 1.05rem;
  line-height: 1.6;
  color: var(--gray-dark);
  margin: 0;
  text-shadow: 0 1px 1px rgba(0,0,0,0.03);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: var(--space-xs) var(--space-sm);
  }

  .logo {
    font-size: 1.2rem;
  }

  .cta-button {
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.9rem;
  }

  .ai-agent {
    bottom: 1rem;
    right: 1rem;
    width: 50px;
    height: 50px;
  }

  #approach-content-panel {
    width: 100vw;
    right: -100vw;
  }

  #approach-content-panel.visible {
    right: 0;
  }
}
