/* Nesy Systems - Master Stylesheet */
/* Monochrome Design with Newspaper-Inspired Typography */

:root {
  /* Brand Colors - Pure Monochrome */
  --primary-black: #000000;
  --primary-white: #FFFFFF;
  --gray-light: #F5F5F5;
  --gray-medium: #CCCCCC;
  --gray-dark: #333333;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Spacing */
  --space-xs: 0.5rem;
  --space-sm: 1rem;
  --space-md: 2rem;
  --space-lg: 4rem;
  --space-xl: 8rem;

  /* Animations */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background-color: var(--primary-black);
  color: var(--primary-white);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* Landing Screen Styles */
.landing-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--primary-black);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: var(--transition-smooth);
}

.landing-content {
  text-align: center;
  max-width: 800px;
  padding: var(--space-md);
}

.company-name {
  font-family: var(--font-display);
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  letter-spacing: -0.02em;
  color: var(--primary-white);
  margin-bottom: var(--space-sm);
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease-out 0.5s forwards;
}

.tagline {
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-weight: 300;
  color: var(--gray-medium);
  margin-bottom: var(--space-lg);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 1.5s forwards;
}

.explore-btn {
  background: transparent;
  border: 2px solid var(--primary-white);
  color: var(--primary-white);
  padding: var(--space-sm) var(--space-lg);
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  cursor: pointer;
  transition: var(--transition-smooth);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 2.3s forwards;
  text-transform: uppercase;
}

.explore-btn:hover {
  background-color: var(--primary-white);
  color: var(--primary-black);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

/* Graph Container */
.graph-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: var(--primary-white);
  z-index: 900;
  transition: var(--transition-smooth);
}

.neural-graph {
  width: 100%;
  height: 100%;
}

/* Header Styles */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-light);
  z-index: 800;
  transition: var(--transition-smooth);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  max-width: 1400px;
  margin: 0 auto;
}

.logo {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 900;
  color: var(--primary-black);
  letter-spacing: -0.02em;
}

.cta-button {
  background-color: var(--primary-black);
  color: var(--primary-white);
  border: none;
  padding: var(--space-sm) var(--space-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  border-radius: 4px;
}

.cta-button:hover {
  background-color: var(--gray-dark);
  transform: translateY(-1px);
}

.pulse {
  animation: pulse 2s infinite;
}

/* AI Agent Orb */
.ai-agent {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  z-index: 700;
  cursor: pointer;
}

.orb-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.3) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: var(--primary-black);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 30%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 70%; animation-delay: 0.6s; }
.particle:nth-child(3) { top: 40%; left: 20%; animation-delay: 1.2s; }
.particle:nth-child(4) { top: 70%; left: 50%; animation-delay: 1.8s; }
.particle:nth-child(5) { top: 30%; left: 80%; animation-delay: 2.4s; }

/* Utility Classes */
.hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

.main-content {
  position: relative;
  z-index: 600;
  background-color: var(--primary-white);
  color: var(--primary-black);
  min-height: 100vh;
  padding-top: 80px;
}

/* Animations */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(0, 0, 0, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-10px) translateX(5px);
  }
  66% {
    transform: translateY(5px) translateX(-5px);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: var(--space-xs) var(--space-sm);
  }

  .logo {
    font-size: 1.2rem;
  }

  .cta-button {
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.9rem;
  }

  .ai-agent {
    bottom: 1rem;
    right: 1rem;
    width: 50px;
    height: 50px;
  }
}
