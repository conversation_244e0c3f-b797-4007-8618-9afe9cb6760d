import './style.css'
import * as d3 from 'd3'

// Nesy Systems - Main Application
class NesySystemsApp {
  constructor() {
    this.currentSection = 'landing'
    this.graphData = this.createGraphData()
    this.init()
  }

  init() {
    this.setupEventListeners()
    this.initializeAIAgent()
  }

  setupEventListeners() {
    // Explore button click handler
    const exploreBtn = document.getElementById('explore-btn')
    if (exploreBtn) {
      exploreBtn.addEventListener('click', () => {
        this.transitionToGraph()
      })
    }

    // Header CTA button
    const ctaButton = document.querySelector('.cta-button')
    if (ctaButton) {
      ctaButton.addEventListener('click', () => {
        this.navigateToBlueprint()
      })
    }

    // AI Agent interaction
    const aiAgent = document.getElementById('ai-agent')
    if (aiAgent) {
      aiAgent.addEventListener('click', () => {
        this.toggleAIAgent()
      })
    }
  }

  transitionToGraph() {
    const landingScreen = document.getElementById('landing-screen')
    const graphContainer = document.getElementById('graph-container')
    const mainHeader = document.getElementById('main-header')

    // Update current section
    this.currentSection = 'graph'

    // Fade out landing screen
    landingScreen.style.opacity = '0'
    landingScreen.style.transform = 'scale(0.95)'

    setTimeout(() => {
      landingScreen.classList.add('hidden')
      graphContainer.classList.remove('hidden')
      mainHeader.classList.remove('hidden')

      // Initialize the complex D3 graph
      this.initializeGraph()

      // Animate graph appearance
      setTimeout(() => {
        graphContainer.style.opacity = '1'
        mainHeader.style.opacity = '1'
      }, 100)
    }, 500)
  }

  createGraphData() {
    const nodes = []
    const links = []

    // Main navigation nodes (large and prominent for desktop)
    const mainNodes = [
      { id: 'blueprint', label: 'Blueprint Sessions', type: 'main', priority: 'high', color: '#000000', size: 80 },
      { id: 'portfolio', label: 'Portfolio', type: 'main', priority: 'medium', color: '#333333', size: 65 },
      { id: 'approach', label: 'Our Approach', type: 'main', priority: 'medium', color: '#333333', size: 65 },
      { id: 'industries', label: 'Industries', type: 'main', priority: 'medium', color: '#333333', size: 65 },
      { id: 'contact', label: 'Contact', type: 'main', priority: 'medium', color: '#333333', size: 65 }
    ]

    // Add main nodes
    nodes.push(...mainNodes)

    // Generate complex organic network of smaller nodes
    const organicNodes = []
    const nodeCategories = [
      'AI', 'ML', 'Systems', 'Architecture', 'Design', 'Innovation', 'Technology', 'Solutions',
      'Algorithms', 'Data', 'Neural', 'Networks', 'Automation', 'Intelligence', 'Computing',
      'Analysis', 'Optimization', 'Integration', 'Development', 'Research', 'Strategy',
      'Implementation', 'Consulting', 'Engineering', 'Prototyping', 'Testing', 'Deployment',
      'Monitoring', 'Security', 'Performance', 'Scalability', 'Reliability', 'Efficiency',
      'Vision', 'Processing', 'Recognition', 'Classification', 'Prediction', 'Modeling',
      'Framework', 'Platform', 'Infrastructure', 'Cloud', 'Edge', 'Mobile', 'Web',
      'API', 'Database', 'Storage', 'Memory', 'Cache', 'Queue', 'Stream', 'Batch',
      'Real-time', 'Async', 'Sync', 'Parallel', 'Distributed', 'Microservices',
      'Containers', 'Orchestration', 'DevOps', 'CI/CD', 'Monitoring', 'Logging',
      'Metrics', 'Alerts', 'Dashboards', 'Visualization', 'Analytics', 'Insights',
      'Business', 'Process', 'Workflow', 'Automation', 'Optimization', 'Efficiency'
    ]

    // Create 180-220 organic nodes for ultra-rich background
    const numOrganicNodes = 180 + Math.floor(Math.random() * 40)
    for (let i = 0; i < numOrganicNodes; i++) {
      const category = nodeCategories[Math.floor(Math.random() * nodeCategories.length)]
      organicNodes.push({
        id: `organic_${i}`,
        label: category,
        type: 'organic',
        color: '#666666',
        size: 3 + Math.random() * 8, // Random size between 3-11
        opacity: 0.3 + Math.random() * 0.4 // Random opacity 0.3-0.7
      })
    }

    nodes.push(...organicNodes)

    // Create connections between main nodes
    const mainConnections = [
      { source: 'blueprint', target: 'portfolio' },
      { source: 'blueprint', target: 'approach' },
      { source: 'blueprint', target: 'industries' },
      { source: 'blueprint', target: 'contact' },
      { source: 'portfolio', target: 'approach' },
      { source: 'approach', target: 'industries' },
      { source: 'industries', target: 'contact' },
      { source: 'portfolio', target: 'contact' }
    ]

    links.push(...mainConnections)

    // Connect organic nodes to main nodes and each other
    organicNodes.forEach((node, index) => {
      // Each organic node connects to 1-3 main nodes
      const numMainConnections = 1 + Math.floor(Math.random() * 3)
      const shuffledMainNodes = [...mainNodes].sort(() => 0.5 - Math.random())

      for (let i = 0; i < numMainConnections; i++) {
        links.push({
          source: node.id,
          target: shuffledMainNodes[i].id,
          strength: 0.1 + Math.random() * 0.3
        })
      }

      // Each organic node connects to 2-5 other organic nodes
      const numOrganicConnections = 2 + Math.floor(Math.random() * 4)
      for (let i = 0; i < numOrganicConnections; i++) {
        const targetIndex = Math.floor(Math.random() * organicNodes.length)
        if (targetIndex !== index) {
          links.push({
            source: node.id,
            target: organicNodes[targetIndex].id,
            strength: 0.05 + Math.random() * 0.2
          })
        }
      }
    })

    return { nodes, links }
  }

  initializeGraph() {
    const container = document.getElementById('neural-graph')
    const width = window.innerWidth
    const height = window.innerHeight

    // Clear any existing SVG
    d3.select(container).selectAll('*').remove()

    const svg = d3.select(container)
      .attr('width', width)
      .attr('height', height)

    // Create gradient definitions for organic feel
    const defs = svg.append('defs')
    const gradient = defs.append('radialGradient')
      .attr('id', 'nodeGradient')
      .attr('cx', '30%')
      .attr('cy', '30%')

    gradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', '#ffffff')
      .attr('stop-opacity', 0.8)

    gradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', '#000000')
      .attr('stop-opacity', 1)

    // Ultra-smooth force simulation for harmonious movement
    const simulation = d3.forceSimulation(this.graphData.nodes)
      .alphaMin(0.0005) // Even lower threshold for complete stability
      .alphaDecay(0.08) // Faster settling to reduce trembling
      .velocityDecay(0.7) // Higher friction for maximum stability
      .force('link', d3.forceLink(this.graphData.links)
        .id(d => d.id)
        .distance(d => {
          if (d.source.type === 'main' && d.target.type === 'main') return 350
          if (d.source.type === 'main' || d.target.type === 'main') return 180
          return 80 + Math.random() * 50
        })
        .strength(0.3)
        .iterations(1) // Reduce iterations for performance
      )
      .force('charge', d3.forceManyBody()
        .strength(d => d.type === 'main' ? -2200 : -70)
        .distanceMax(400) // Increased for better separation
        .theta(0.9) // Higher theta for faster approximation
      )
      .force('center', d3.forceCenter(width / 2, height / 2).strength(0.1))
      .force('collision', d3.forceCollide()
        .radius(d => d.type === 'main' ? d.size + 25 : d.size + 3)
        .strength(0.7)
        .iterations(1) // Single iteration for performance
      )

    // Create curved, organic links
    const linkGroup = svg.append('g').attr('class', 'links')

    // Organic curved paths (subtle, flowing)
    const organicLinks = linkGroup.selectAll('.organic-link')
      .data(this.graphData.links.filter(d =>
        this.graphData.nodes.find(n => n.id === d.source.id || n.id === d.source)?.type === 'organic' ||
        this.graphData.nodes.find(n => n.id === d.target.id || n.id === d.target)?.type === 'organic'
      ))
      .enter().append('path')
      .attr('class', 'organic-link')
      .attr('stroke', '#e8e8e8')
      .attr('stroke-width', d => 0.3 + Math.random() * 0.4)
      .attr('fill', 'none')
      .attr('opacity', 0)
      .style('stroke-dasharray', d => Math.random() > 0.7 ? '2,3' : 'none')

    // Main curved paths (prominent, flowing)
    const mainLinks = linkGroup.selectAll('.main-link')
      .data(this.graphData.links.filter(d =>
        this.graphData.nodes.find(n => n.id === d.source.id || n.id === d.source)?.type === 'main' &&
        this.graphData.nodes.find(n => n.id === d.target.id || n.id === d.target)?.type === 'main'
      ))
      .enter().append('path')
      .attr('class', 'main-link')
      .attr('stroke', '#444444')
      .attr('stroke-width', 3)
      .attr('fill', 'none')
      .attr('opacity', 0)
      .style('stroke-linecap', 'round')
      .style('filter', 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))')

    // Create node groups
    const nodeGroup = svg.append('g').attr('class', 'nodes')

    // Organic nodes (small, subtle)
    const organicNodes = nodeGroup.selectAll('.organic-node')
      .data(this.graphData.nodes.filter(d => d.type === 'organic'))
      .enter().append('g')
      .attr('class', 'organic-node')

    organicNodes.append('circle')
      .attr('r', d => d.size)
      .attr('fill', d => d.color)
      .attr('opacity', d => d.opacity)
      .style('opacity', 0)
      .each(function(d) {
        // Add subtle breathing to random organic nodes
        if (Math.random() > 0.8) {
          d3.select(this)
            .style('--base-opacity', d.opacity)
            .style('animation', `breathe ${3 + Math.random() * 4}s ease-in-out infinite`)
            .style('animation-delay', `${Math.random() * 2}s`)
        }
      })

    // Main nodes (large, prominent, interactive)
    const mainNodes = nodeGroup.selectAll('.main-node')
      .data(this.graphData.nodes.filter(d => d.type === 'main'))
      .enter().append('g')
      .attr('class', 'main-node')
      .style('cursor', 'pointer')
      .call(d3.drag()
        .on('start', this.dragstarted.bind(this))
        .on('drag', this.dragged.bind(this))
        .on('end', this.dragended.bind(this)))

    // Solid background circle to hide connections behind nodes
    mainNodes.append('circle')
      .attr('class', 'node-background')
      .attr('r', d => d.size + 8)
      .attr('fill', '#ffffff')
      .style('opacity', 0)

    // Main node circles with enhanced gradient
    mainNodes.append('circle')
      .attr('class', 'node-main')
      .attr('r', d => d.size)
      .attr('fill', 'url(#nodeGradient)')
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 4)
      .style('opacity', 0)
      .style('filter', 'drop-shadow(0 6px 12px rgba(0,0,0,0.4))')

    // Create prominent text labels with background
    const textGroups = mainNodes.append('g')
      .attr('class', 'text-group')
      .style('opacity', 0)

    // Text background for better readability and depth
    textGroups.append('rect')
      .attr('class', 'text-background')
      .attr('rx', 8)
      .attr('ry', 8)
      .attr('fill', 'rgba(255, 255, 255, 0.95)')
      .attr('stroke', 'rgba(0, 0, 0, 0.2)')
      .attr('stroke-width', 1)
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))')

    // Main text labels with enhanced typography
    const textElements = textGroups.append('text')
      .text(d => d.label)
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('fill', '#000000')
      .attr('font-size', d => d.priority === 'high' ? '18px' : '16px')
      .attr('font-weight', '700')
      .attr('font-family', 'Inter, -apple-system, BlinkMacSystemFont, sans-serif')
      .style('pointer-events', 'none')

    // Position text outside the circles for better prominence
    textGroups.attr('transform', d => `translate(0, ${d.size + 25})`)

    // Calculate and set background rectangle dimensions
    textElements.each(function(d) {
      const bbox = this.getBBox()
      const padding = 12
      d3.select(this.parentNode).select('.text-background')
        .attr('x', bbox.x - padding)
        .attr('y', bbox.y - padding/2)
        .attr('width', bbox.width + padding * 2)
        .attr('height', bbox.height + padding)
    })

    // Enhanced hover effects for main nodes
    mainNodes
      .on('mouseenter', function(event, d) {
        const node = d3.select(this)

        // Scale up both circles
        node.select('.node-background')
          .transition().duration(300)
          .attr('r', (d.size + 8) * 1.15)

        node.select('.node-main')
          .transition().duration(300)
          .attr('r', d.size * 1.15)
          .style('filter', 'drop-shadow(0 8px 16px rgba(0,0,0,0.5))')

        // Enhance text background
        node.select('.text-background')
          .transition().duration(300)
          .attr('fill', 'rgba(255, 255, 255, 1)')
          .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))')

        // Make text bolder
        node.select('text')
          .transition().duration(300)
          .attr('font-size', d => d.priority === 'high' ? '20px' : '18px')
      })
      .on('mouseleave', function(event, d) {
        const node = d3.select(this)

        // Return both circles to normal
        node.select('.node-background')
          .transition().duration(300)
          .attr('r', d.size + 8)

        node.select('.node-main')
          .transition().duration(300)
          .attr('r', d.size)
          .style('filter', 'drop-shadow(0 6px 12px rgba(0,0,0,0.4))')

        // Return text background to normal
        node.select('.text-background')
          .transition().duration(300)
          .attr('fill', 'rgba(255, 255, 255, 0.95)')
          .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))')

        // Return text to normal
        node.select('text')
          .transition().duration(300)
          .attr('font-size', d => d.priority === 'high' ? '18px' : '16px')
      })
      .on('click', (event, d) => {
        this.navigateToSection(d.id)
      })

    // Animate the complex network appearance
    this.animateComplexGraphAppearance(organicLinks, mainLinks, organicNodes, mainNodes)

    // Pre-calculate stable curve parameters for each link
    this.graphData.links.forEach(link => {
      // Store stable curve parameters that won't change
      link.curvature = 0.15 + Math.random() * 0.25
      link.sweep = Math.random() > 0.5 ? 1 : 0
      link.offsetMultiplier = 0.8 + Math.random() * 0.4
      link.controlDirection = Math.random() > 0.5 ? 1 : -1
    })

    // Helper function to create stable curved paths
    const createStableCurvedPath = (d) => {
      const dx = d.target.x - d.source.x
      const dy = d.target.y - d.source.y
      const dr = Math.sqrt(dx * dx + dy * dy)

      // For main links, use smooth quadratic curves with stable control points
      if (d.source.type === 'main' && d.target.type === 'main') {
        const midX = (d.source.x + d.target.x) / 2
        const midY = (d.source.y + d.target.y) / 2
        const offset = 40 * d.offsetMultiplier
        const controlX = midX + (dy / dr) * offset * d.controlDirection
        const controlY = midY - (dx / dr) * offset * d.controlDirection

        return `M${d.source.x.toFixed(1)},${d.source.y.toFixed(1)} Q${controlX.toFixed(1)},${controlY.toFixed(1)} ${d.target.x.toFixed(1)},${d.target.y.toFixed(1)}`
      }

      // For organic links, use stable arc paths
      const radius = dr * d.curvature
      return `M${d.source.x.toFixed(1)},${d.source.y.toFixed(1)} A${radius.toFixed(1)},${radius.toFixed(1)} 0 0,${d.sweep} ${d.target.x.toFixed(1)},${d.target.y.toFixed(1)}`
    }

    // Optimized simulation tick with stable curves
    simulation.on('tick', () => {
      // Update curved paths with stable parameters
      organicLinks.attr('d', createStableCurvedPath)
      mainLinks.attr('d', createStableCurvedPath)

      // Update node positions with sub-pixel precision for smoothness
      organicNodes
        .attr('transform', d => `translate(${d.x.toFixed(1)},${d.y.toFixed(1)})`)

      mainNodes
        .attr('transform', d => `translate(${d.x.toFixed(1)},${d.y.toFixed(1)})`)
    })

    // Store simulation for later use
    this.simulation = simulation
  }

  animateComplexGraphAppearance(organicLinks, mainLinks, organicNodes, mainNodes) {
    // First wave: Organic particles appear
    organicNodes.selectAll('circle')
      .transition()
      .duration(3000)
      .delay((d, i) => i * 15) // Faster stagger for more particles
      .style('opacity', d => d.opacity)
      .ease(d3.easeCircleOut)

    // Second wave: Organic connections flow in harmoniously
    organicLinks
      .style('stroke-dasharray', '0,1000') // Start invisible
      .transition()
      .duration(2500)
      .delay((d, i) => 1000 + i * 12)
      .attr('opacity', d => 0.15 + Math.random() * 0.1)
      .style('stroke-dasharray', '1000,0') // Fade in smoothly
      .ease(d3.easeQuadInOut)

    // Third wave: Main nodes emerge dramatically
    setTimeout(() => {
      // Background circles appear first (solid)
      mainNodes.selectAll('.node-background')
        .transition()
        .duration(1200)
        .delay((d, i) => i * 300)
        .style('opacity', 1)
        .ease(d3.easeCircleOut)

      // Main circles with bounce
      mainNodes.selectAll('.node-main')
        .transition()
        .duration(1500)
        .delay((d, i) => i * 300 + 200)
        .style('opacity', 1)
        .attr('r', d => d.size)
        .ease(d3.easeBounceOut)

      // Text labels with back-out effect
      mainNodes.selectAll('.text-group')
        .transition()
        .duration(1200)
        .delay((d, i) => i * 300 + 500)
        .style('opacity', 1)
        .ease(d3.easeBackOut.overshoot(1.3))

      // Main curved connections with elegant flow
      mainLinks.each(function(d, i) {
        const path = d3.select(this)
        const totalLength = this.getTotalLength()

        // Set up for smooth drawing animation
        path
          .style('stroke-dasharray', `${totalLength} ${totalLength}`)
          .style('stroke-dashoffset', totalLength)
          .attr('opacity', 0)

        // Animate the path drawing with harmonious timing
        path
          .transition()
          .duration(1800)
          .delay(1200 + i * 400) // Longer delays for more elegance
          .attr('opacity', 0.75)
          .transition()
          .duration(2000)
          .style('stroke-dashoffset', 0)
          .ease(d3.easeCircleOut)
          .on('end', function() {
            // Remove dash array after animation for clean curves
            d3.select(this).style('stroke-dasharray', 'none')
          })
      })
    }, 1500)

    // Add pulsing effect to Blueprint Sessions (revenue focus)
    setTimeout(() => {
      const blueprintNode = mainNodes.filter(d => d.id === 'blueprint')
      this.addPulsingEffect(blueprintNode)
    }, 3000)
  }

  addPulsingEffect(node) {
    const circle = node.select('circle')

    function pulse() {
      circle
        .transition()
        .duration(1500)
        .attr('r', d => d.size * 1.1)
        .style('opacity', 0.8)
        .transition()
        .duration(1500)
        .attr('r', d => d.size)
        .style('opacity', 1)
        .on('end', pulse)
    }

    pulse()
  }

  dragstarted(event, d) {
    if (!event.active) this.simulation.alphaTarget(0.3).restart()
    d.fx = d.x
    d.fy = d.y

    // Highlight the dragged node
    const node = d3.select(event.currentTarget)
    node.select('.node-background')
      .transition().duration(200)
      .attr('r', (d.size + 8) * 1.3)

    node.select('.node-main')
      .transition().duration(200)
      .attr('r', d.size * 1.3)
      .style('filter', 'drop-shadow(0 8px 16px rgba(0,0,0,0.5))')
  }

  dragged(event, d) {
    d.fx = event.x
    d.fy = event.y
  }

  dragended(event, d) {
    if (!event.active) this.simulation.alphaTarget(0)
    d.fx = null
    d.fy = null

    // Return node to normal size
    const node = d3.select(event.currentTarget)
    node.select('.node-background')
      .transition().duration(200)
      .attr('r', d.size + 8)

    node.select('.node-main')
      .transition().duration(200)
      .attr('r', d.size)
      .style('filter', 'drop-shadow(0 6px 12px rgba(0,0,0,0.4))')
  }

  navigateToSection(sectionId) {
    console.log(`Navigating to section: ${sectionId}`)
    this.currentSection = sectionId

    switch(sectionId) {
      case 'blueprint':
        this.navigateToBlueprint()
        break
      case 'portfolio':
        console.log('Portfolio section - Coming in Phase 2')
        break
      case 'approach':
        this.transitionToApproach()
        break
      case 'industries':
        console.log('Industries section - Coming in Phase 2')
        break
      case 'contact':
        console.log('Contact section - Coming in Phase 2')
        break
    }
  }

  transitionToApproach() {
    console.log('🚀 Transitioning to Our Approach deep-dive!')

    // Find the approach node
    const approachNode = this.graphData.nodes.find(n => n.id === 'approach')
    if (!approachNode) return

    // Phase 1: Fade out other nodes and links
    this.fadeOutOtherElements(approachNode)

    // Phase 2: Migrate approach node to left side (after 1 second)
    setTimeout(() => {
      this.migrateApproachNode(approachNode)
    }, 1000)

    // Phase 3: Create and animate new sub-graph (after 2 seconds)
    setTimeout(() => {
      this.createApproachSubGraph(approachNode)
    }, 2000)

    // Phase 4: Show content panel (after 3 seconds)
    setTimeout(() => {
      this.showApproachContentPanel()
    }, 3000)
  }

  navigateToBlueprint() {
    // This will be implemented in the next task
    console.log('Blueprint Sessions page - Next task!')
    alert('Blueprint Sessions page coming next! This will be our revenue-generating page.')
  }

  initializeAIAgent() {
    const particles = document.querySelectorAll('.particle')

    // Add subtle floating animation variations
    particles.forEach((particle, index) => {
      particle.style.animationDelay = `${index * 0.6}s`
      particle.style.animationDuration = `${3 + (index * 0.5)}s`
    })
  }

  toggleAIAgent() {
    console.log('AI Agent clicked - Basic chatbot coming in Phase 2')

    // For now, just show a simple interaction
    const orb = document.querySelector('.orb-container')
    orb.style.transform = 'scale(1.1)'
    orb.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.3)'

    setTimeout(() => {
      orb.style.transform = 'scale(1)'
      orb.style.boxShadow = 'none'
    }, 200)

    // Show a temporary message
    this.showTemporaryMessage('AI Agent coming soon! For now, click "Book Blueprint Session" to get started.')
  }

  showTemporaryMessage(message) {
    // Create a temporary notification
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      bottom: 100px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      font-size: 14px;
      max-width: 300px;
      z-index: 1001;
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.3s ease;
    `
    notification.textContent = message
    document.body.appendChild(notification)

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1'
      notification.style.transform = 'translateY(0)'
    }, 100)

    // Remove after 4 seconds
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateY(20px)'
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 300)
    }, 4000)
  }

  fadeOutOtherElements(keepNode) {
    const svg = d3.select('#neural-graph')

    // Fade out all nodes except the selected one
    svg.selectAll('.main-node')
      .filter(d => d.id !== keepNode.id)
      .transition()
      .duration(1000)
      .style('opacity', 0)
      .ease(d3.easeQuadOut)

    // Fade out all organic nodes
    svg.selectAll('.organic-node')
      .transition()
      .duration(800)
      .style('opacity', 0)
      .ease(d3.easeQuadOut)

    // Fade out all links
    svg.selectAll('.organic-link, .main-link')
      .transition()
      .duration(600)
      .style('opacity', 0)
      .ease(d3.easeQuadOut)
  }

  migrateApproachNode(approachNode) {
    const svg = d3.select('#neural-graph')
    const width = window.innerWidth
    const height = window.innerHeight

    // Target position: ensure it stays well within screen bounds
    const targetX = Math.min(width * 0.2, 300)  // Max 300px from left or 20% of screen
    const targetY = height * 0.35  // Slightly higher for better visibility

    // Gentle, smooth migration without spring effects
    const nodeElement = svg.selectAll('.main-node')
      .filter(d => d.id === 'approach')

    // Single smooth transition with gentle easing
    nodeElement
      .transition()
      .duration(1500)
      .ease(d3.easeQuadInOut)  // Smooth, gentle easing
      .attr('transform', `translate(${targetX},${targetY})`)
      .on('end', function() {
        // Add subtle glow effect when settled
        d3.select(this).select('.node-main')
          .style('filter', 'drop-shadow(0 8px 20px rgba(0,0,0,0.3)) drop-shadow(0 0 15px rgba(255,255,255,0.2))')
      })

    // Update the node's position in data
    approachNode.x = targetX
    approachNode.y = targetY
    approachNode.fx = targetX
    approachNode.fy = targetY
  }

  createApproachSubGraph(anchorNode) {
    const svg = d3.select('#neural-graph')
    const width = window.innerWidth
    const height = window.innerHeight

    // Create approach-specific sub-nodes with enhanced sizing
    const approachSubNodes = [
      { id: 'philosophy', label: 'Philosophy', type: 'sub-main', size: 55, color: '#333333' },
      { id: 'process', label: 'Process', type: 'sub-main', size: 55, color: '#333333' },
      { id: 'methodology', label: 'Methodology', type: 'sub-main', size: 55, color: '#333333' },
      { id: 'principles', label: 'First Principles', type: 'sub-main', size: 55, color: '#333333' },
      { id: 'different', label: 'What Makes Us Different', type: 'sub-main', size: 55, color: '#333333' }
    ]

    // Make "Our Approach" anchor node more prominent
    anchorNode.size = 90  // Bigger than original main nodes

    // Position sub-nodes in a flowing pattern around the anchor (keeping on screen)
    const centerX = anchorNode.x + 180 // Fixed offset from anchor
    const centerY = anchorNode.y
    const radius = 100 // Smaller radius for better screen fit

    approachSubNodes.forEach((node, i) => {
      const angle = (i / approachSubNodes.length) * 2 * Math.PI - Math.PI / 2
      let nodeX = centerX + Math.cos(angle) * radius
      let nodeY = centerY + Math.sin(angle) * radius

      // Ensure all nodes stay well within screen bounds
      nodeX = Math.max(150, Math.min(nodeX, width - 300)) // More conservative bounds
      nodeY = Math.max(120, Math.min(nodeY, height - 120))

      node.x = nodeX
      node.y = nodeY
    })

    // Create organic background nodes for approach
    const approachOrganicNodes = []
    const approachKeywords = [
      'Systems Thinking', 'Human-AI', 'First Principles', 'Deep Analysis', 'Solution Exploration',
      'Rapid Prototyping', 'Implementation', 'Knowledge Transfer', 'Real Deliverables',
      'Graph-based AI', 'Cross-industry', 'Technical Execution', 'Architecture', 'Design',
      'Innovation', 'Research', 'Strategy', 'Consulting', 'Engineering', 'Testing',
      'Deployment', 'Monitoring', 'Security', 'Performance', 'Scalability', 'Reliability',
      'Efficiency', 'Vision', 'Processing', 'Recognition', 'Classification', 'Prediction',
      'Modeling', 'Framework', 'Platform', 'Infrastructure', 'Analysis', 'Optimization',
      'Integration', 'Development', 'Prototyping', 'Blueprint', 'Methodology', 'Process',
      'Workflow', 'Automation', 'Intelligence', 'Computing', 'Algorithms', 'Neural Networks'
    ]

    // Create 80 organic nodes for richer background (within screen bounds)
    for (let i = 0; i < 80; i++) {
      const keyword = approachKeywords[Math.floor(Math.random() * approachKeywords.length)]

      // Generate positions within safe bounds
      let orgX = centerX + (Math.random() - 0.5) * 500
      let orgY = centerY + (Math.random() - 0.5) * 350

      // Keep within screen bounds
      orgX = Math.max(50, Math.min(orgX, width - 250)) // Account for content panel
      orgY = Math.max(50, Math.min(orgY, height - 50))

      approachOrganicNodes.push({
        id: `approach_organic_${i}`,
        label: keyword,
        type: 'sub-organic',
        color: '#888888',
        size: 3 + Math.random() * 6,
        opacity: 0.2 + Math.random() * 0.3,
        x: orgX,
        y: orgY
      })
    }

    // Add all nodes to the graph
    this.approachNodes = [...approachSubNodes, ...approachOrganicNodes]
    this.animateApproachSubGraph(svg, anchorNode)
  }

  animateApproachSubGraph(svg, anchorNode) {
    // Update the anchor node size and disable dragging
    const anchorElement = svg.selectAll('.main-node')
      .filter(d => d.id === 'approach')

    // Disable dragging on anchor node and update its appearance
    anchorElement
      .on('.drag', null) // Remove drag handlers
      .select('.node-background')
      .transition()
      .duration(500)
      .attr('r', anchorNode.size + 8)

    anchorElement
      .select('.node-main')
      .transition()
      .duration(500)
      .attr('r', anchorNode.size)

    // Create sub-node groups
    const subMainNodes = svg.append('g').attr('class', 'approach-sub-nodes')
      .selectAll('.sub-main-node')
      .data(this.approachNodes.filter(d => d.type === 'sub-main'))
      .enter().append('g')
      .attr('class', 'sub-main-node')
      .style('cursor', 'pointer')
      .attr('transform', d => `translate(${anchorNode.x},${anchorNode.y})`) // Start from anchor
      .style('opacity', 0)

    // Add solid background circles to hide connections behind nodes
    subMainNodes.append('circle')
      .attr('class', 'sub-node-background')
      .attr('r', d => d.size + 10)  // Larger background for better occlusion
      .attr('fill', '#ffffff')
      .style('opacity', 1)  // Fully opaque

    // Add main gradient circles
    subMainNodes.append('circle')
      .attr('class', 'sub-node-main')
      .attr('r', d => d.size)
      .attr('fill', 'url(#nodeGradient)')  // Same gradient as main nodes
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 4)
      .style('filter', 'drop-shadow(0 6px 12px rgba(0,0,0,0.4))')
      .style('opacity', 1)  // Fully opaque

    // Add text labels
    const textGroups = subMainNodes.append('g')
      .attr('class', 'sub-text-group')
      .attr('transform', d => `translate(0, ${d.size + 20})`)

    textGroups.append('rect')
      .attr('class', 'sub-text-background')
      .attr('rx', 6)
      .attr('ry', 6)
      .attr('fill', 'rgba(255, 255, 255, 0.95)')
      .attr('stroke', 'rgba(0, 0, 0, 0.2)')
      .attr('stroke-width', 1)

    const textElements = textGroups.append('text')
      .text(d => d.label)
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('fill', '#000000')
      .attr('font-size', '14px')
      .attr('font-weight', '600')
      .attr('font-family', 'Inter, sans-serif')

    // Set text background sizes
    textElements.each(function(d) {
      const bbox = this.getBBox()
      const padding = 8
      d3.select(this.parentNode).select('.sub-text-background')
        .attr('x', bbox.x - padding)
        .attr('y', bbox.y - padding/2)
        .attr('width', bbox.width + padding * 2)
        .attr('height', bbox.height + padding)
    })

    // Add click handlers for sub-nodes
    subMainNodes.on('click', (event, d) => {
      this.highlightApproachSection(d.id)
    })

    // Animate sub-nodes to their positions with gentle emergence
    subMainNodes
      .transition()
      .duration(1200)
      .delay((d, i) => i * 150)
      .attr('transform', d => `translate(${d.x},${d.y})`)
      .style('opacity', 1)
      .ease(d3.easeCircleOut)  // Gentle, smooth easing like landing page

    // Add organic background nodes
    setTimeout(() => {
      this.addApproachOrganicNodes(svg, anchorNode)
    }, 800)
  }

  addApproachOrganicNodes(svg, anchorNode) {
    const organicNodes = svg.append('g').attr('class', 'approach-organic-nodes')
      .selectAll('.sub-organic-node')
      .data(this.approachNodes.filter(d => d.type === 'sub-organic'))
      .enter().append('g')
      .attr('class', 'sub-organic-node')
      .attr('transform', d => `translate(${d.x},${d.y})`)
      .style('opacity', 0)

    organicNodes.append('circle')
      .attr('r', d => d.size)
      .attr('fill', d => d.color)
      .attr('opacity', d => d.opacity)

    // Animate organic nodes
    organicNodes
      .transition()
      .duration(2000)
      .delay((d, i) => i * 30)
      .style('opacity', 1)
      .ease(d3.easeCircleOut)

    // Add connecting lines
    this.addApproachConnections(svg, anchorNode)
  }

  addApproachConnections(svg, anchorNode) {
    const connections = []
    const subMainNodes = this.approachNodes.filter(d => d.type === 'sub-main')
    const organicNodes = this.approachNodes.filter(d => d.type === 'sub-organic')

    // Connect anchor to sub-main nodes
    subMainNodes.forEach(node => {
      connections.push({ source: anchorNode, target: node, type: 'main-to-sub' })
    })

    // Connect some organic nodes to sub-main nodes
    organicNodes.forEach(organic => {
      const nearestSubMain = subMainNodes.reduce((nearest, subMain) => {
        const distToOrganic = Math.sqrt(Math.pow(organic.x - subMain.x, 2) + Math.pow(organic.y - subMain.y, 2))
        const distToNearest = nearest ? Math.sqrt(Math.pow(organic.x - nearest.x, 2) + Math.pow(organic.y - nearest.y, 2)) : Infinity
        return distToOrganic < distToNearest ? subMain : nearest
      }, null)

      if (nearestSubMain && Math.random() > 0.6) {
        connections.push({ source: organic, target: nearestSubMain, type: 'organic-to-sub' })
      }
    })

    // Draw connections with stable, flowing curves
    const linkGroup = svg.append('g').attr('class', 'approach-links')

    const links = linkGroup.selectAll('.approach-link')
      .data(connections)
      .enter().append('path')
      .attr('class', 'approach-link')
      .attr('stroke', d => d.type === 'main-to-sub' ? '#666666' : '#cccccc')
      .attr('stroke-width', d => d.type === 'main-to-sub' ? 3 : 1)
      .attr('fill', 'none')
      .attr('opacity', 0)
      .style('stroke-linecap', 'round')
      .attr('d', d => {
        const dx = d.target.x - d.source.x
        const dy = d.target.y - d.source.y
        const dr = Math.sqrt(dx * dx + dy * dy)

        // Create smooth quadratic curves like the main graph
        const midX = (d.source.x + d.target.x) / 2
        const midY = (d.source.y + d.target.y) / 2
        const offset = 20 // Gentle curve offset
        const controlX = midX + (dy / dr) * offset
        const controlY = midY - (dx / dr) * offset

        return `M${d.source.x.toFixed(1)},${d.source.y.toFixed(1)} Q${controlX.toFixed(1)},${controlY.toFixed(1)} ${d.target.x.toFixed(1)},${d.target.y.toFixed(1)}`
      })

    // Animate connections
    links
      .transition()
      .duration(1000)
      .delay((d, i) => i * 50)
      .attr('opacity', d => d.type === 'main-to-sub' ? 0.8 : 0.3)
      .ease(d3.easeQuadOut)
  }

  showApproachContentPanel() {
    // Create content panel HTML
    const contentPanel = document.createElement('div')
    contentPanel.id = 'approach-content-panel'
    contentPanel.innerHTML = `
      <div class="content-panel-header">
        <h2>Our Approach</h2>
        <button id="back-to-main" class="back-button">← Back to Main</button>
      </div>
      <div class="content-panel-body">
        <div class="approach-intro">
          <h3>Systems Thinking Meets AI Innovation</h3>
          <p>We combine deep systems engineering expertise with cutting-edge AI capabilities to deliver real-world solutions that work.</p>
        </div>
        <div class="approach-sections">
          <div class="approach-section" data-section="philosophy">
            <h4>Philosophy</h4>
            <p>Human + AI augmentation through first principles methodology</p>
          </div>
          <div class="approach-section" data-section="process">
            <h4>Process</h4>
            <p>Deep Analysis → Solution Exploration → Rapid Prototyping → Implementation</p>
          </div>
          <div class="approach-section" data-section="methodology">
            <h4>Methodology</h4>
            <p>Proprietary graph-based AI systems with cross-industry expertise</p>
          </div>
          <div class="approach-section" data-section="principles">
            <h4>First Principles</h4>
            <p>Breaking down complex problems to fundamental truths and building up</p>
          </div>
          <div class="approach-section" data-section="different">
            <h4>What Makes Us Different</h4>
            <p>Real deliverables, not just advice. Guaranteed technical execution.</p>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(contentPanel)

    // Add event listener for back button
    document.getElementById('back-to-main').addEventListener('click', () => {
      this.returnToMainGraph()
    })

    // Animate panel in
    setTimeout(() => {
      contentPanel.classList.add('visible')
    }, 100)
  }

  highlightApproachSection(sectionId) {
    // Remove previous highlights
    document.querySelectorAll('.approach-section').forEach(section => {
      section.classList.remove('highlighted')
    })

    // Highlight the corresponding content section
    const targetSection = document.querySelector(`[data-section="${sectionId}"]`)
    if (targetSection) {
      targetSection.classList.add('highlighted')

      // Smooth scroll to the section
      targetSection.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })

      // Remove highlight after 3 seconds
      setTimeout(() => {
        targetSection.classList.remove('highlighted')
      }, 3000)
    }
  }

  returnToMainGraph() {
    // Remove approach elements
    d3.select('#neural-graph').selectAll('.approach-sub-nodes, .approach-organic-nodes, .approach-links').remove()

    // Remove content panel
    const panel = document.getElementById('approach-content-panel')
    if (panel) {
      panel.classList.remove('visible')
      setTimeout(() => panel.remove(), 300)
    }

    // Clear the entire graph and regenerate from scratch
    d3.select('#neural-graph').selectAll('*').remove()

    // Reset current section and regenerate main graph
    this.currentSection = 'graph'
    this.graphData = this.createGraphData()
    this.initializeGraph()
  }

  // Handle window resize
  handleResize() {
    if (this.currentSection !== 'landing') {
      // Regenerate graph data to get new organic network
      this.graphData = this.createGraphData()
      this.initializeGraph()
    }
  }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  const app = new NesySystemsApp()

  // Handle window resize
  window.addEventListener('resize', () => {
    app.handleResize()
  })

  console.log('Nesy Systems - AI Architecture for Real World Impact')
  console.log('Phase 1: Landing page and basic graph navigation implemented')
})
