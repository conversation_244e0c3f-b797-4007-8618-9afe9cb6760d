import './style.css'
import * as d3 from 'd3'

// Nesy Systems - Main Application
class NesySystemsApp {
  constructor() {
    this.currentSection = 'landing'
    this.graphData = this.createGraphData()
    this.init()
  }

  init() {
    this.setupEventListeners()
    this.initializeAIAgent()
  }

  setupEventListeners() {
    // Explore button click handler
    const exploreBtn = document.getElementById('explore-btn')
    if (exploreBtn) {
      exploreBtn.addEventListener('click', () => {
        this.transitionToGraph()
      })
    }

    // Header CTA button
    const ctaButton = document.querySelector('.cta-button')
    if (ctaButton) {
      ctaButton.addEventListener('click', () => {
        this.navigateToBlueprint()
      })
    }

    // AI Agent interaction
    const aiAgent = document.getElementById('ai-agent')
    if (aiAgent) {
      aiAgent.addEventListener('click', () => {
        this.toggleAIAgent()
      })
    }
  }

  transitionToGraph() {
    const landingScreen = document.getElementById('landing-screen')
    const graphContainer = document.getElementById('graph-container')
    const mainHeader = document.getElementById('main-header')

    // Fade out landing screen
    landingScreen.style.opacity = '0'
    landingScreen.style.transform = 'scale(0.95)'

    setTimeout(() => {
      landingScreen.classList.add('hidden')
      graphContainer.classList.remove('hidden')
      mainHeader.classList.remove('hidden')

      // Initialize the D3 graph
      this.initializeGraph()

      // Animate graph appearance
      setTimeout(() => {
        graphContainer.style.opacity = '1'
        mainHeader.style.opacity = '1'
      }, 100)
    }, 500)
  }

  createGraphData() {
    return {
      nodes: [
        { id: 'blueprint', label: 'Blueprint Sessions', x: 0, y: -100, priority: 'high', color: '#000000' },
        { id: 'portfolio', label: 'Portfolio', x: -150, y: 50, priority: 'medium', color: '#333333' },
        { id: 'approach', label: 'Our Approach', x: 150, y: 50, priority: 'medium', color: '#333333' },
        { id: 'industries', label: 'Industries', x: -100, y: 150, priority: 'medium', color: '#333333' },
        { id: 'contact', label: 'Contact', x: 100, y: 150, priority: 'medium', color: '#333333' }
      ],
      links: [
        { source: 'blueprint', target: 'portfolio' },
        { source: 'blueprint', target: 'approach' },
        { source: 'blueprint', target: 'industries' },
        { source: 'blueprint', target: 'contact' },
        { source: 'portfolio', target: 'approach' },
        { source: 'industries', target: 'contact' }
      ]
    }
  }

  initializeGraph() {
    const container = document.getElementById('neural-graph')
    const width = window.innerWidth
    const height = window.innerHeight

    // Clear any existing SVG
    d3.select(container).selectAll('*').remove()

    const svg = d3.select(container)
      .attr('width', width)
      .attr('height', height)

    // Create force simulation
    const simulation = d3.forceSimulation(this.graphData.nodes)
      .force('link', d3.forceLink(this.graphData.links).id(d => d.id).distance(150))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(60))

    // Create links
    const links = svg.append('g')
      .selectAll('line')
      .data(this.graphData.links)
      .enter().append('line')
      .attr('stroke', '#cccccc')
      .attr('stroke-width', 2)
      .attr('opacity', 0)

    // Create nodes
    const nodes = svg.append('g')
      .selectAll('g')
      .data(this.graphData.nodes)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer')
      .call(d3.drag()
        .on('start', this.dragstarted.bind(this))
        .on('drag', this.dragged.bind(this))
        .on('end', this.dragended.bind(this)))

    // Add circles to nodes
    nodes.append('circle')
      .attr('r', d => d.priority === 'high' ? 40 : 30)
      .attr('fill', d => d.color)
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 3)
      .style('opacity', 0)

    // Add labels to nodes
    nodes.append('text')
      .text(d => d.label)
      .attr('text-anchor', 'middle')
      .attr('dy', '.35em')
      .attr('fill', '#ffffff')
      .attr('font-size', d => d.priority === 'high' ? '14px' : '12px')
      .attr('font-weight', 'bold')
      .style('opacity', 0)
      .style('pointer-events', 'none')

    // Add click handlers
    nodes.on('click', (event, d) => {
      this.navigateToSection(d.id)
    })

    // Animate appearance
    this.animateGraphAppearance(links, nodes)

    // Update positions on simulation tick
    simulation.on('tick', () => {
      links
        .attr('x1', d => d.source.x)
        .attr('y1', d => d.source.y)
        .attr('x2', d => d.target.x)
        .attr('y2', d => d.target.y)

      nodes
        .attr('transform', d => `translate(${d.x},${d.y})`)
    })
  }

  animateGraphAppearance(links, nodes) {
    // Animate links
    links.transition()
      .duration(1000)
      .delay((d, i) => i * 100)
      .attr('opacity', 0.6)

    // Animate nodes
    nodes.selectAll('circle')
      .transition()
      .duration(800)
      .delay((d, i) => i * 150)
      .style('opacity', 1)
      .attr('r', d => d.priority === 'high' ? 40 : 30)

    nodes.selectAll('text')
      .transition()
      .duration(800)
      .delay((d, i) => i * 150 + 200)
      .style('opacity', 1)
  }

  dragstarted(event, d) {
    if (!event.active) d3.select('#neural-graph').select('g').selectAll('g').select('circle').transition().duration(200).attr('r', d => d.priority === 'high' ? 45 : 35)
    d.fx = d.x
    d.fy = d.y
  }

  dragged(event, d) {
    d.fx = event.x
    d.fy = event.y
  }

  dragended(event, d) {
    if (!event.active) d3.select('#neural-graph').select('g').selectAll('g').select('circle').transition().duration(200).attr('r', d => d.priority === 'high' ? 40 : 30)
    d.fx = null
    d.fy = null
  }

  navigateToSection(sectionId) {
    console.log(`Navigating to section: ${sectionId}`)
    this.currentSection = sectionId

    // For now, we'll just log the navigation
    // In Phase 2, we'll implement full section loading
    switch(sectionId) {
      case 'blueprint':
        this.navigateToBlueprint()
        break
      case 'portfolio':
        console.log('Portfolio section - Coming in Phase 2')
        break
      case 'approach':
        console.log('Approach section - Coming in Phase 2')
        break
      case 'industries':
        console.log('Industries section - Coming in Phase 2')
        break
      case 'contact':
        console.log('Contact section - Coming in Phase 2')
        break
    }
  }

  navigateToBlueprint() {
    // This will be implemented in the next task
    console.log('Blueprint Sessions page - Next task!')
    alert('Blueprint Sessions page coming next! This will be our revenue-generating page.')
  }

  initializeAIAgent() {
    const particles = document.querySelectorAll('.particle')

    // Add subtle floating animation variations
    particles.forEach((particle, index) => {
      particle.style.animationDelay = `${index * 0.6}s`
      particle.style.animationDuration = `${3 + (index * 0.5)}s`
    })
  }

  toggleAIAgent() {
    console.log('AI Agent clicked - Basic chatbot coming in Phase 2')

    // For now, just show a simple interaction
    const orb = document.querySelector('.orb-container')
    orb.style.transform = 'scale(1.1)'
    orb.style.boxShadow = '0 0 20px rgba(0, 0, 0, 0.3)'

    setTimeout(() => {
      orb.style.transform = 'scale(1)'
      orb.style.boxShadow = 'none'
    }, 200)

    // Show a temporary message
    this.showTemporaryMessage('AI Agent coming soon! For now, click "Book Blueprint Session" to get started.')
  }

  showTemporaryMessage(message) {
    // Create a temporary notification
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      bottom: 100px;
      right: 20px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      font-size: 14px;
      max-width: 300px;
      z-index: 1001;
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.3s ease;
    `
    notification.textContent = message
    document.body.appendChild(notification)

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1'
      notification.style.transform = 'translateY(0)'
    }, 100)

    // Remove after 4 seconds
    setTimeout(() => {
      notification.style.opacity = '0'
      notification.style.transform = 'translateY(20px)'
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 300)
    }, 4000)
  }

  // Handle window resize
  handleResize() {
    if (this.currentSection === 'graph') {
      this.initializeGraph()
    }
  }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  const app = new NesySystemsApp()

  // Handle window resize
  window.addEventListener('resize', () => {
    app.handleResize()
  })

  console.log('Nesy Systems - AI Architecture for Real World Impact')
  console.log('Phase 1: Landing page and basic graph navigation implemented')
})
